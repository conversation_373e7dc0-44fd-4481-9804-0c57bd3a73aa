# i18n MCP Server

A high-performance Model Context Protocol (MCP) server for managing internationalization (i18n) translation files. Built with TypeScript and designed for efficiency, this server provides intelligent translation management capabilities through MCP tools.

## 🚀 Features

### Core Capabilities
- **High-Performance Indexing**: O(1) translation lookups with in-memory indexing
- **Real-time File Watching**: Automatic detection and processing of translation file changes
- **Advanced Search**: Fast search across translation keys and values with scoring
- **Structure Validation**: Cross-language consistency checking and auto-fixing
- **Context Retrieval**: Hierarchical context with parent/child/sibling relationships
- **Batch Operations**: Atomic multi-operation transactions with rollback
- **Usage Analysis**: Dead code detection and duplicate value identification

### MCP Tools
1. **`search_translation`** - Search for translations across keys and values
2. **`get_translation_context`** - Retrieve translations with hierarchical context
3. **`update_translation`** - Update translation values with validation
4. **`validate_structure`** - Check consistency across language files
5. **`get_stats`** - Get server and index statistics

## 📦 Installation

```bash
# Clone the repository
git clone <repository-url>
cd i18n-mcp

# Install dependencies
npm install

# Build the project
npm run build
```

## 🏃‍♂️ Quick Start

### 1. Prepare Translation Files

Create a `locales` directory with JSON translation files:

```
locales/
├── en.json    # Base language
├── es.json    # Spanish translations
├── fr.json    # French translations
└── ...
```

Example `en.json`:
```json
{
  "common": {
    "buttons": {
      "submit": "Submit",
      "cancel": "Cancel"
    }
  },
  "auth": {
    "login": {
      "title": "Sign In"
    }
  }
}
```

### 2. Start the MCP Server

```bash
# Start with default settings (watches ./locales)
npm start

# Start with custom directory and debug mode
node dist/index.js --dir ./i18n --debug

# Show help
node dist/index.js --help
```

### 3. Use with MCP Clients

The server communicates via STDIO transport and can be used with any MCP-compatible client like Claude Desktop.

## 🔧 Configuration

### Command Line Options

```bash
Usage: i18n-mcp [options] [translation-directory]

Options:
  -d, --dir <path>           Translation files directory (default: ./locales)
  -b, --base-language <lang> Base language for structure template (default: en)
  -n, --name <name>          Server name (default: i18n-mcp)
  -v, --version <version>    Server version (default: 1.0.0)
  --debug                    Enable debug logging
  -h, --help                 Show help message
```

### Environment Variables

```bash
I18N_MCP_DIR              # Translation directory
I18N_MCP_BASE_LANGUAGE    # Base language
I18N_MCP_DEBUG            # Enable debug mode (true/false)
```

## 🛠️ MCP Tools Usage

### Search Translations

```json
{
  "tool": "search_translation",
  "arguments": {
    "query": "submit",
    "scope": "both",
    "languages": ["en", "es"],
    "maxResults": 10,
    "caseSensitive": false
  }
}
```

### Get Translation Context

```json
{
  "tool": "get_translation_context",
  "arguments": {
    "keyPath": "common.buttons.submit",
    "contextDepth": 1,
    "languages": "all"
  }
}
```

### Update Translation

```json
{
  "tool": "update_translation",
  "arguments": {
    "keyPath": "common.buttons.submit",
    "updates": {
      "en": "Submit Form",
      "es": "Enviar Formulario"
    },
    "validateStructure": true
  }
}
```

### Validate Structure

```json
{
  "tool": "validate_structure",
  "arguments": {
    "baseLanguage": "en",
    "fix": false
  }
}
```

## 🧪 Testing

Run the test suite to verify functionality:

```bash
# Run simple functionality test
npx tsx simple-test.ts

# Run development server test
npx tsx test-server.ts
```

## 📊 Performance Features

- **LRU Caching**: Configurable cache for frequently accessed translations
- **Binary Search**: Optimized prefix searches on sorted key indices
- **Debounced File Operations**: Prevents thrashing during rapid file changes
- **Memory Efficient**: Minimal memory footprint with smart indexing

## 🏗️ Architecture

### Core Components

1. **TranslationIndex**: High-performance in-memory index with O(1) lookups
2. **FileWatcher**: Chokidar-based file monitoring with debounced processing
3. **MCPServer**: Main server implementation with tool registration
4. **JsonOperations**: Safe JSON parsing and manipulation utilities
5. **PathParser**: Efficient dot-notation path parsing with caching

### Data Flow

```
Translation Files → File Watcher → Translation Index → MCP Tools → Client
```

## 🔍 Example Output

### Search Results
```json
{
  "query": "submit",
  "resultsCount": 1,
  "results": [
    {
      "keyPath": "common.buttons.submit",
      "matchType": "both",
      "score": 1,
      "translations": {
        "en": { "value": "Submit", "file": "locales/en.json" },
        "es": { "value": "Enviar", "file": "locales/es.json" }
      }
    }
  ]
}
```

### Structure Validation
```json
{
  "valid": false,
  "summary": {
    "missingKeysCount": 3,
    "extraKeysCount": 0,
    "typeMismatchesCount": 0
  },
  "details": {
    "missingKeys": {
      "fr": ["common.buttons.edit", "auth.register.title"]
    }
  }
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

- Built with the [Model Context Protocol SDK](https://github.com/modelcontextprotocol/typescript-sdk)
- File watching powered by [Chokidar](https://github.com/paulmillr/chokidar)
- Type validation with [Zod](https://github.com/colinhacks/zod)
