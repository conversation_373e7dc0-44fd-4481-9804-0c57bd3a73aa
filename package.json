{"name": "i18n-mcp", "version": "1.0.0", "description": "High-performance MCP server for managing internationalization (i18n) translation files", "main": "dist/index.js", "type": "module", "bin": {"i18n-mcp": "dist/index.js"}, "scripts": {"build": "tsc", "build:watch": "tsc --watch", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "lint": "echo '<PERSON><PERSON> not configured yet'", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:server": "npx tsx test-server.ts"}, "keywords": ["mcp", "model-context-protocol", "i18n", "internationalization", "translation", "localization", "typescript"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "chokidar": "^4.0.3", "zod": "^3.25.49"}, "devDependencies": {"@types/node": "^22.15.29", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "nodemon": "^3.1.10", "tsx": "^4.19.4", "typescript": "^5.8.3", "vitest": "^2.1.8"}, "engines": {"node": ">=18.0.0"}}