{"version": 3, "file": "path-parser.d.ts", "sourceRoot": "", "sources": ["../../src/utils/path-parser.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH;;GAEG;AACH,qBAAa,UAAU;IACrB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAA+B;IAC5D,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAS;IAE/C;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE;IAmBpC;;;;OAIG;IACH,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,MAAM;IAIvC;;;;OAIG;IACH,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAQ7C;;;;OAIG;IACH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAK3C;;;;;OAKG;IACH,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,OAAO;IAOhE;;;;;OAKG;IACH,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,GAAG,OAAO;IAO5E;;;;OAIG;IACH,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE;IAW5C;;;;OAIG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAIrC;;;;OAIG;IACH,MAAM,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM;IA0B/C;;;;OAIG;IACH,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAQtC;;;;OAIG;IACH,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAuBrC;;OAEG;IACH,MAAM,CAAC,UAAU,IAAI,IAAI;IAIzB;;;OAGG;IACH,MAAM,CAAC,aAAa,IAAI;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAA;KAAE;CAM1D;AAED;;GAEG;AACH,wBAAgB,QAAQ,CAAC,CAAC,SAAS,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,EACxD,IAAI,EAAE,CAAC,EACP,IAAI,EAAE,MAAM,GACX,CAAC,GAAG,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,CAOlC;AAED;;GAEG;AACH,qBAAa,WAAW,CAAC,CAAC;IAGZ,OAAO,CAAC,WAAW;IAF/B,OAAO,CAAC,KAAK,CAAW;gBAEJ,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM;IAEvD;;;OAGG;IACH,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI;IAKrB;;;;OAIG;IACH,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,OAAO;IASxB;;;;OAIG;IACH,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,MAAM;IASxB;;;OAGG;IACH,OAAO,IAAI,CAAC,EAAE;IAId;;;OAGG;IACH,IAAI,MAAM,IAAI,MAAM,CAEnB;IAED;;OAEG;IACH,KAAK,IAAI,IAAI;IAIb,OAAO,CAAC,YAAY;CAgBrB"}