/**
 * Utilities for parsing and manipulating dot-notation paths
 */
/**
 * Memory-efficient path parsing with caching
 */
export class PathParser {
    static CACHE = new Map();
    static MAX_CACHE_SIZE = 10000;
    /**
     * Parse a dot-notation path into segments
     * @param path - The path to parse (e.g., "common.buttons.submit")
     * @returns Array of path segments
     */
    static parse(path) {
        if (this.CACHE.has(path)) {
            return this.CACHE.get(path);
        }
        const segments = path.split('.');
        this.CACHE.set(path, segments);
        // Prevent memory leaks by limiting cache size
        if (this.CACHE.size > this.MAX_CACHE_SIZE) {
            const firstKey = this.CACHE.keys().next().value;
            if (firstKey) {
                this.CACHE.delete(firstKey);
            }
        }
        return segments;
    }
    /**
     * Join path segments into a dot-notation path
     * @param segments - Array of path segments
     * @returns Joined path string
     */
    static join(segments) {
        return segments.join('.');
    }
    /**
     * Get the parent path of a given path
     * @param path - The path to get parent for
     * @returns Parent path or null if no parent
     */
    static getParent(path) {
        const segments = this.parse(path);
        if (segments.length <= 1) {
            return null;
        }
        return this.join(segments.slice(0, -1));
    }
    /**
     * Get the last segment of a path
     * @param path - The path to get last segment for
     * @returns Last segment
     */
    static getLastSegment(path) {
        const segments = this.parse(path);
        return segments[segments.length - 1] || '';
    }
    /**
     * Check if one path is a child of another
     * @param childPath - The potential child path
     * @param parentPath - The potential parent path
     * @returns True if childPath is a child of parentPath
     */
    static isChildOf(childPath, parentPath) {
        if (childPath === parentPath) {
            return false;
        }
        return childPath.startsWith(parentPath + '.');
    }
    /**
     * Check if one path is a descendant of another
     * @param descendantPath - The potential descendant path
     * @param ancestorPath - The potential ancestor path
     * @returns True if descendantPath is a descendant of ancestorPath
     */
    static isDescendantOf(descendantPath, ancestorPath) {
        if (descendantPath === ancestorPath) {
            return false;
        }
        return descendantPath.startsWith(ancestorPath + '.');
    }
    /**
     * Get all possible parent paths for a given path
     * @param path - The path to get parents for
     * @returns Array of parent paths from immediate to root
     */
    static getAllParents(path) {
        const segments = this.parse(path);
        const parents = [];
        for (let i = segments.length - 1; i > 0; i--) {
            parents.push(this.join(segments.slice(0, i)));
        }
        return parents;
    }
    /**
     * Get the depth of a path (number of segments)
     * @param path - The path to measure
     * @returns Depth of the path
     */
    static getDepth(path) {
        return this.parse(path).length;
    }
    /**
     * Get the common prefix of multiple paths
     * @param paths - Array of paths to find common prefix for
     * @returns Common prefix path or empty string if no common prefix
     */
    static getCommonPrefix(paths) {
        if (paths.length === 0) {
            return '';
        }
        if (paths.length === 1) {
            return paths[0] || '';
        }
        const segmentArrays = paths.map(path => this.parse(path));
        const minLength = Math.min(...segmentArrays.map(segments => segments.length));
        const commonSegments = [];
        for (let i = 0; i < minLength; i++) {
            const segment = segmentArrays[0]?.[i];
            if (segment && segmentArrays.every(segments => segments[i] === segment)) {
                commonSegments.push(segment);
            }
            else {
                break;
            }
        }
        return this.join(commonSegments);
    }
    /**
     * Normalize a path by removing empty segments and trimming
     * @param path - The path to normalize
     * @returns Normalized path
     */
    static normalize(path) {
        return path
            .split('.')
            .filter(segment => segment.trim().length > 0)
            .map(segment => segment.trim())
            .join('.');
    }
    /**
     * Validate that a path is well-formed
     * @param path - The path to validate
     * @returns True if path is valid
     */
    static isValid(path) {
        if (!path || typeof path !== 'string') {
            return false;
        }
        // Check for invalid characters
        if (/[^a-zA-Z0-9._-]/.test(path)) {
            return false;
        }
        // Check for consecutive dots
        if (path.includes('..')) {
            return false;
        }
        // Check for leading or trailing dots
        if (path.startsWith('.') || path.endsWith('.')) {
            return false;
        }
        return true;
    }
    /**
     * Clear the internal cache (useful for testing or memory management)
     */
    static clearCache() {
        this.CACHE.clear();
    }
    /**
     * Get cache statistics
     * @returns Object with cache size and hit rate info
     */
    static getCacheStats() {
        return {
            size: this.CACHE.size,
            maxSize: this.MAX_CACHE_SIZE
        };
    }
}
/**
 * Debounce utility for file watching
 */
export function debounce(func, wait) {
    let timeout;
    return (...args) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
}
/**
 * Binary search utilities for sorted operations
 */
export class SortedArray {
    compareFunc;
    items = [];
    constructor(compareFunc) {
        this.compareFunc = compareFunc;
    }
    /**
     * Insert an item in the correct sorted position
     * @param item - Item to insert
     */
    insert(item) {
        const index = this.binarySearch(item);
        this.items.splice(index, 0, item);
    }
    /**
     * Remove an item from the array
     * @param item - Item to remove
     * @returns True if item was found and removed
     */
    remove(item) {
        const index = this.indexOf(item);
        if (index >= 0) {
            this.items.splice(index, 1);
            return true;
        }
        return false;
    }
    /**
     * Find the index of an item
     * @param item - Item to find
     * @returns Index of item or -1 if not found
     */
    indexOf(item) {
        const index = this.binarySearch(item);
        const foundItem = this.items[index];
        if (index < this.items.length && foundItem && this.compareFunc(foundItem, item) === 0) {
            return index;
        }
        return -1;
    }
    /**
     * Get all items as an array
     * @returns Copy of the internal array
     */
    toArray() {
        return [...this.items];
    }
    /**
     * Get the number of items
     * @returns Length of the array
     */
    get length() {
        return this.items.length;
    }
    /**
     * Clear all items
     */
    clear() {
        this.items = [];
    }
    binarySearch(target) {
        let left = 0;
        let right = this.items.length;
        while (left < right) {
            const mid = Math.floor((left + right) / 2);
            const midItem = this.items[mid];
            if (midItem && this.compareFunc(midItem, target) < 0) {
                left = mid + 1;
            }
            else {
                right = mid;
            }
        }
        return left;
    }
}
//# sourceMappingURL=path-parser.js.map