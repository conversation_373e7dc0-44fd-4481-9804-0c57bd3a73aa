/**
 * High-performance in-memory translation index
 */
import { EventEmitter } from 'events';
import { TranslationEntry, IndexedTranslation, TranslationIndexConfig, SearchOptions, SearchResult, ContextOptions, ContextResult, BatchOperation, ValidationResult, UsageAnalysis } from '../types/translation.js';
/**
 * High-performance translation index with O(1) lookups and advanced search
 */
export declare class TranslationIndex extends EventEmitter {
    private readonly flatIndex;
    private readonly reverseIndex;
    private readonly structureTemplate;
    private readonly cache;
    private sortedKeys;
    private keysDirty;
    private readonly config;
    constructor(config: TranslationIndexConfig);
    /**
     * O(1) lookup with caching
     */
    get(keyPath: string, language?: string): TranslationEntry | IndexedTranslation | undefined;
    /**
     * Set a translation value
     */
    set(keyPath: string, language: string, value: any, metadata?: Partial<TranslationEntry>): void;
    /**
     * Delete a translation
     */
    delete(keyPath: string, language?: string): boolean;
    /**
     * Search translations with advanced options
     */
    search(query: string, options?: SearchOptions): Promise<SearchResult[]>;
    /**
     * Get translation context with hierarchical information
     */
    getContext(keyPath: string, options: ContextOptions): Promise<ContextResult | null>;
    /**
     * Get all available languages
     */
    getLanguages(): string[];
    /**
     * Get all translation keys
     */
    getKeys(): string[];
    /**
     * Get statistics about the index
     */
    getStats(): {
        totalKeys: number;
        totalTranslations: number;
        languages: string[];
        cacheSize: number;
        memoryUsage: string;
    };
    /**
     * Clear all data
     */
    clear(): void;
    private ensureSortedKeys;
    private updateReverseIndex;
    private updateStructureTemplate;
    private invalidateCache;
    private calculateKeyScore;
    private calculateValueScore;
    /**
     * Batch operations for atomic updates
     */
    batchUpdate(operations: BatchOperation[]): Promise<{
        success: boolean;
        errors: string[];
    }>;
    /**
     * Validate structure consistency across languages
     */
    validateStructure(options?: {
        baseLanguage?: string;
        autoFix?: boolean;
    }): Promise<ValidationResult>;
    /**
     * Analyze usage patterns and find optimization opportunities
     */
    analyzeUsage(options?: {
        codebasePath?: string;
        checkDuplicates?: boolean;
    }): Promise<UsageAnalysis>;
    /**
     * Optimized prefix search using binary search
     */
    searchByPrefix(prefix: string): string[];
    private binarySearchStart;
    private binarySearchEnd;
}
//# sourceMappingURL=translation-index.d.ts.map