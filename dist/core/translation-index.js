/**
 * High-performance in-memory translation index
 */
import { EventEmitter } from 'events';
import { IndexError } from '../types/translation.js';
import { PathParser } from '../utils/path-parser.js';
/**
 * LRU Cache implementation for translation lookups
 */
class LRUCache {
    cache = new Map();
    maxSize;
    constructor(maxSize = 10000) {
        this.maxSize = maxSize;
    }
    get(key) {
        const value = this.cache.get(key);
        if (value !== undefined) {
            // Move to end (most recently used)
            this.cache.delete(key);
            this.cache.set(key, value);
        }
        return value;
    }
    set(key, value) {
        if (this.cache.has(key)) {
            this.cache.delete(key);
        }
        else if (this.cache.size >= this.maxSize) {
            // Remove least recently used (first item)
            const firstKey = this.cache.keys().next().value;
            if (firstKey !== undefined) {
                this.cache.delete(firstKey);
            }
        }
        this.cache.set(key, value);
    }
    has(key) {
        return this.cache.has(key);
    }
    clear() {
        this.cache.clear();
    }
    get size() {
        return this.cache.size;
    }
}
/**
 * High-performance translation index with O(1) lookups and advanced search
 */
export class TranslationIndex extends EventEmitter {
    flatIndex = new Map();
    reverseIndex = new Map(); // value -> paths
    structureTemplate = new Map();
    cache;
    // Binary search optimized sorted keys for prefix operations
    sortedKeys = [];
    keysDirty = false;
    config;
    constructor(config) {
        super();
        this.config = {
            maxCacheSize: 10000,
            debug: false,
            ...config,
            baseLanguage: config.baseLanguage || 'en'
        };
        this.cache = new LRUCache(this.config.maxCacheSize);
        if (this.config.debug) {
            console.log(`🔍 TranslationIndex initialized with base language: ${this.config.baseLanguage}`);
        }
    }
    /**
     * O(1) lookup with caching
     */
    get(keyPath, language) {
        const cacheKey = `${keyPath}:${language || 'all'}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        const entry = this.flatIndex.get(keyPath);
        if (!entry) {
            return undefined;
        }
        const result = language ? entry[language] : entry;
        this.cache.set(cacheKey, result);
        return result;
    }
    /**
     * Set a translation value
     */
    set(keyPath, language, value, metadata) {
        if (!PathParser.isValid(keyPath)) {
            throw new IndexError(`Invalid key path: ${keyPath}`);
        }
        let entry = this.flatIndex.get(keyPath);
        if (!entry) {
            entry = {};
            this.flatIndex.set(keyPath, entry);
            this.keysDirty = true;
        }
        const translationEntry = {
            value,
            file: metadata?.file || '',
            line: metadata?.line || 0,
            column: metadata?.column || 0,
            lastModified: Date.now()
        };
        entry[language] = translationEntry;
        // Update reverse index for value searches
        this.updateReverseIndex(keyPath, value);
        // Update structure template if this is the base language
        if (language === this.config.baseLanguage) {
            this.updateStructureTemplate(keyPath, value);
        }
        // Clear cache for affected entries
        this.invalidateCache(keyPath);
        this.emit('set', { keyPath, language, value, metadata: translationEntry });
    }
    /**
     * Delete a translation
     */
    delete(keyPath, language) {
        const entry = this.flatIndex.get(keyPath);
        if (!entry) {
            return false;
        }
        if (language) {
            // Delete specific language
            if (entry[language]) {
                delete entry[language];
                // If no languages left, remove the entire entry
                if (Object.keys(entry).length === 0) {
                    this.flatIndex.delete(keyPath);
                    this.keysDirty = true;
                }
                this.invalidateCache(keyPath);
                this.emit('delete', { keyPath, language });
                return true;
            }
        }
        else {
            // Delete all languages for this key
            this.flatIndex.delete(keyPath);
            this.keysDirty = true;
            this.invalidateCache(keyPath);
            this.emit('delete', { keyPath });
            return true;
        }
        return false;
    }
    /**
     * Search translations with advanced options
     */
    async search(query, options = { scope: 'both' }) {
        const results = [];
        const queryLower = query.toLowerCase();
        const caseSensitive = options.caseSensitive || false;
        const searchQuery = caseSensitive ? query : queryLower;
        this.ensureSortedKeys();
        for (const keyPath of this.sortedKeys) {
            const entry = this.flatIndex.get(keyPath);
            if (!entry)
                continue;
            let matchType = null;
            let score = 0;
            // Check key match
            const keyToCheck = caseSensitive ? keyPath : keyPath.toLowerCase();
            const keyMatch = options.scope === 'keys' || options.scope === 'both';
            if (keyMatch && keyToCheck.includes(searchQuery)) {
                matchType = 'key';
                score = this.calculateKeyScore(keyToCheck, searchQuery);
            }
            // Check value match
            const valueMatch = options.scope === 'values' || options.scope === 'both';
            if (valueMatch) {
                for (const [lang, translationEntry] of Object.entries(entry)) {
                    if (options.languages && !options.languages.includes(lang)) {
                        continue;
                    }
                    const valueStr = String(translationEntry.value);
                    const valueToCheck = caseSensitive ? valueStr : valueStr.toLowerCase();
                    if (valueToCheck.includes(searchQuery)) {
                        const valueScore = this.calculateValueScore(valueToCheck, searchQuery);
                        if (valueScore > score) {
                            score = valueScore;
                            matchType = matchType === 'key' ? 'both' : 'value';
                        }
                    }
                }
            }
            if (matchType && score > 0) {
                // Filter by languages if specified
                const filteredEntry = {};
                for (const [lang, translationEntry] of Object.entries(entry)) {
                    if (!options.languages || options.languages.includes(lang)) {
                        filteredEntry[lang] = translationEntry;
                    }
                }
                if (Object.keys(filteredEntry).length > 0) {
                    results.push({
                        keyPath,
                        translations: filteredEntry,
                        score,
                        matchType
                    });
                }
            }
            // Limit results
            if (options.maxResults && results.length >= options.maxResults) {
                break;
            }
        }
        // Sort by score (descending)
        results.sort((a, b) => b.score - a.score);
        return results;
    }
    /**
     * Get translation context with hierarchical information
     */
    async getContext(keyPath, options) {
        const entry = this.flatIndex.get(keyPath);
        if (!entry) {
            return null;
        }
        // Filter by languages
        const filteredEntry = {};
        for (const [lang, translationEntry] of Object.entries(entry)) {
            if (!options.languages || options.languages.includes(lang)) {
                filteredEntry[lang] = translationEntry;
            }
        }
        const result = {
            keyPath,
            translations: filteredEntry,
            children: [],
            siblings: []
        };
        // Get parent context
        const parentPath = PathParser.getParent(keyPath);
        if (parentPath && options.depth > 0) {
            const parentEntry = this.flatIndex.get(parentPath);
            if (parentEntry) {
                const filteredParentEntry = {};
                for (const [lang, translationEntry] of Object.entries(parentEntry)) {
                    if (!options.languages || options.languages.includes(lang)) {
                        filteredParentEntry[lang] = translationEntry;
                    }
                }
                result.parent = {
                    keyPath: parentPath,
                    translations: filteredParentEntry
                };
            }
        }
        // Get children and siblings
        this.ensureSortedKeys();
        for (const otherKeyPath of this.sortedKeys) {
            // Check for children
            if (PathParser.isChildOf(otherKeyPath, keyPath)) {
                const childEntry = this.flatIndex.get(otherKeyPath);
                if (childEntry) {
                    const filteredChildEntry = {};
                    for (const [lang, translationEntry] of Object.entries(childEntry)) {
                        if (!options.languages || options.languages.includes(lang)) {
                            filteredChildEntry[lang] = translationEntry;
                        }
                    }
                    if (Object.keys(filteredChildEntry).length > 0) {
                        result.children.push({
                            keyPath: otherKeyPath,
                            translations: filteredChildEntry
                        });
                    }
                }
            }
            // Check for siblings
            if (parentPath && PathParser.isChildOf(otherKeyPath, parentPath) && otherKeyPath !== keyPath) {
                const siblingEntry = this.flatIndex.get(otherKeyPath);
                if (siblingEntry) {
                    const filteredSiblingEntry = {};
                    for (const [lang, translationEntry] of Object.entries(siblingEntry)) {
                        if (!options.languages || options.languages.includes(lang)) {
                            filteredSiblingEntry[lang] = translationEntry;
                        }
                    }
                    if (Object.keys(filteredSiblingEntry).length > 0) {
                        result.siblings.push({
                            keyPath: otherKeyPath,
                            translations: filteredSiblingEntry
                        });
                    }
                }
            }
        }
        return result;
    }
    /**
     * Get all available languages
     */
    getLanguages() {
        const languages = new Set();
        for (const entry of this.flatIndex.values()) {
            for (const lang of Object.keys(entry)) {
                languages.add(lang);
            }
        }
        return Array.from(languages).sort();
    }
    /**
     * Get all translation keys
     */
    getKeys() {
        this.ensureSortedKeys();
        return [...this.sortedKeys];
    }
    /**
     * Get statistics about the index
     */
    getStats() {
        let totalTranslations = 0;
        for (const entry of this.flatIndex.values()) {
            totalTranslations += Object.keys(entry).length;
        }
        return {
            totalKeys: this.flatIndex.size,
            totalTranslations,
            languages: this.getLanguages(),
            cacheSize: this.cache.size,
            memoryUsage: `${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`
        };
    }
    /**
     * Clear all data
     */
    clear() {
        this.flatIndex.clear();
        this.reverseIndex.clear();
        this.structureTemplate.clear();
        this.cache.clear();
        this.sortedKeys = [];
        this.keysDirty = false;
        this.emit('clear');
    }
    // Private helper methods
    ensureSortedKeys() {
        if (this.keysDirty) {
            this.sortedKeys = Array.from(this.flatIndex.keys()).sort();
            this.keysDirty = false;
        }
    }
    updateReverseIndex(keyPath, value) {
        const valueStr = String(value).toLowerCase();
        if (!this.reverseIndex.has(valueStr)) {
            this.reverseIndex.set(valueStr, new Set());
        }
        this.reverseIndex.get(valueStr).add(keyPath);
    }
    updateStructureTemplate(keyPath, value) {
        // This is a simplified structure template - could be enhanced
        this.structureTemplate.set(keyPath, typeof value);
    }
    invalidateCache(keyPath) {
        // Clear cache entries related to this key path
        const keysToDelete = [];
        for (const [cacheKey] of this.cache['cache']) {
            if (String(cacheKey).startsWith(keyPath + ':') || String(cacheKey) === keyPath + ':all') {
                keysToDelete.push(String(cacheKey));
            }
        }
        for (const key of keysToDelete) {
            this.cache['cache'].delete(key);
        }
    }
    calculateKeyScore(key, query) {
        if (key === query)
            return 1.0;
        if (key.startsWith(query))
            return 0.9;
        if (key.includes(query))
            return 0.7;
        return 0.5;
    }
    calculateValueScore(value, query) {
        if (value === query)
            return 1.0;
        if (value.startsWith(query))
            return 0.8;
        if (value.includes(query))
            return 0.6;
        return 0.4;
    }
    /**
     * Batch operations for atomic updates
     */
    async batchUpdate(operations) {
        const errors = [];
        const backup = new Map(this.flatIndex);
        try {
            for (const operation of operations) {
                try {
                    if (operation.type === 'set') {
                        if (!operation.language || operation.value === undefined) {
                            throw new Error('Set operation requires language and value');
                        }
                        this.set(operation.keyPath, operation.language, operation.value);
                    }
                    else if (operation.type === 'delete') {
                        this.delete(operation.keyPath, operation.language);
                    }
                }
                catch (error) {
                    errors.push(`Operation failed for ${operation.keyPath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
            if (errors.length > 0) {
                // Rollback on any errors
                this.flatIndex.clear();
                backup.forEach((value, key) => this.flatIndex.set(key, value));
                this.keysDirty = true;
                this.cache.clear();
                return { success: false, errors };
            }
            this.keysDirty = true;
            this.cache.clear();
            this.emit('batchUpdate', operations);
            return { success: true, errors: [] };
        }
        catch (error) {
            // Rollback on error
            this.flatIndex.clear();
            backup.forEach((value, key) => this.flatIndex.set(key, value));
            this.keysDirty = true;
            this.cache.clear();
            return {
                success: false,
                errors: [`Batch operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
            };
        }
    }
    /**
     * Validate structure consistency across languages
     */
    async validateStructure(options = {}) {
        const baseLanguage = options.baseLanguage || this.config.baseLanguage;
        const autoFix = options.autoFix || false;
        const result = {
            valid: true,
            missingKeys: {},
            extraKeys: {},
            typeMismatches: [],
            structuralIssues: []
        };
        const languages = this.getLanguages();
        const allKeys = this.getKeys();
        // Get base language keys as the template
        const baseKeys = new Set();
        for (const keyPath of allKeys) {
            const entry = this.flatIndex.get(keyPath);
            if (entry && entry[baseLanguage]) {
                baseKeys.add(keyPath);
            }
        }
        // Check each language against the base
        for (const language of languages) {
            if (language === baseLanguage)
                continue;
            const languageKeys = new Set();
            const missingKeys = [];
            const extraKeys = [];
            // Collect keys for this language
            for (const keyPath of allKeys) {
                const entry = this.flatIndex.get(keyPath);
                if (entry && entry[language]) {
                    languageKeys.add(keyPath);
                }
            }
            // Find missing keys (in base but not in language)
            for (const baseKey of baseKeys) {
                if (!languageKeys.has(baseKey)) {
                    missingKeys.push(baseKey);
                    result.valid = false;
                }
            }
            // Find extra keys (in language but not in base)
            for (const langKey of languageKeys) {
                if (!baseKeys.has(langKey)) {
                    extraKeys.push(langKey);
                    result.valid = false;
                }
            }
            if (missingKeys.length > 0) {
                result.missingKeys[language] = missingKeys;
            }
            if (extraKeys.length > 0) {
                result.extraKeys[language] = extraKeys;
            }
            // Check type consistency
            for (const keyPath of baseKeys) {
                const baseEntry = this.flatIndex.get(keyPath);
                const langEntry = this.flatIndex.get(keyPath);
                if (baseEntry && langEntry && baseEntry[baseLanguage] && langEntry[language]) {
                    const baseType = typeof baseEntry[baseLanguage].value;
                    const langType = typeof langEntry[language].value;
                    if (baseType !== langType) {
                        result.typeMismatches.push({
                            keyPath,
                            expected: baseType,
                            actual: { [language]: langType }
                        });
                        result.valid = false;
                    }
                }
            }
        }
        // Auto-fix if requested
        if (autoFix && !result.valid) {
            const fixOperations = [];
            // Add missing keys with placeholder values
            for (const [language, missingKeys] of Object.entries(result.missingKeys)) {
                for (const keyPath of missingKeys) {
                    const baseEntry = this.flatIndex.get(keyPath);
                    if (baseEntry && baseEntry[baseLanguage]) {
                        fixOperations.push({
                            type: 'set',
                            keyPath,
                            language,
                            value: `[MISSING: ${baseEntry[baseLanguage].value}]`
                        });
                    }
                }
            }
            if (fixOperations.length > 0) {
                await this.batchUpdate(fixOperations);
                result.structuralIssues.push(`Auto-fixed ${fixOperations.length} missing translations`);
            }
        }
        return result;
    }
    /**
     * Analyze usage patterns and find optimization opportunities
     */
    async analyzeUsage(options = {}) {
        const checkDuplicates = options.checkDuplicates !== false;
        const result = {
            totalKeys: this.flatIndex.size,
            unusedKeys: [], // Would need codebase scanning to implement
            duplicateValues: [],
            missingTranslations: {},
            languageStats: {}
        };
        const languages = this.getLanguages();
        const allKeys = this.getKeys();
        // Calculate language statistics
        for (const language of languages) {
            let translatedKeys = 0;
            for (const keyPath of allKeys) {
                const entry = this.flatIndex.get(keyPath);
                if (entry && entry[language]) {
                    translatedKeys++;
                }
            }
            result.languageStats[language] = {
                totalKeys: allKeys.length,
                translatedKeys,
                completeness: allKeys.length > 0 ? translatedKeys / allKeys.length : 0
            };
        }
        // Find missing translations
        for (const language of languages) {
            const missingKeys = [];
            for (const keyPath of allKeys) {
                const entry = this.flatIndex.get(keyPath);
                if (!entry || !entry[language]) {
                    missingKeys.push(keyPath);
                }
            }
            if (missingKeys.length > 0) {
                result.missingTranslations[language] = missingKeys;
            }
        }
        // Find duplicate values
        if (checkDuplicates) {
            const valueMap = new Map();
            for (const keyPath of allKeys) {
                const entry = this.flatIndex.get(keyPath);
                if (entry) {
                    for (const [language, translationEntry] of Object.entries(entry)) {
                        const valueStr = String(translationEntry.value);
                        const key = `${language}:${valueStr}`;
                        if (!valueMap.has(key)) {
                            valueMap.set(key, []);
                        }
                        valueMap.get(key).push(keyPath);
                    }
                }
            }
            for (const [key, keyPaths] of valueMap) {
                if (keyPaths.length > 1) {
                    const [, value] = key.split(':', 2);
                    result.duplicateValues.push({
                        value,
                        keys: keyPaths
                    });
                }
            }
        }
        return result;
    }
    /**
     * Optimized prefix search using binary search
     */
    searchByPrefix(prefix) {
        this.ensureSortedKeys();
        const startIdx = this.binarySearchStart(prefix);
        const endIdx = this.binarySearchEnd(prefix);
        return this.sortedKeys.slice(startIdx, endIdx + 1);
    }
    binarySearchStart(prefix) {
        let left = 0;
        let right = this.sortedKeys.length;
        while (left < right) {
            const mid = Math.floor((left + right) / 2);
            const key = this.sortedKeys[mid];
            if (key && key < prefix) {
                left = mid + 1;
            }
            else {
                right = mid;
            }
        }
        return left;
    }
    binarySearchEnd(prefix) {
        let left = 0;
        let right = this.sortedKeys.length;
        const prefixEnd = prefix + '\uffff'; // Unicode max character
        while (left < right) {
            const mid = Math.floor((left + right) / 2);
            const key = this.sortedKeys[mid];
            if (key && key < prefixEnd) {
                left = mid + 1;
            }
            else {
                right = mid;
            }
        }
        return left - 1;
    }
}
//# sourceMappingURL=translation-index.js.map