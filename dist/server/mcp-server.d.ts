/**
 * Main MCP server implementation for i18n translation management
 */
import { TranslationIndex } from '../core/translation-index.js';
import { TranslationFileWatcher } from '../core/file-watcher.js';
import { ServerConfig } from '../types/translation.js';
/**
 * High-level MCP server for translation management
 */
export declare class TranslationMCPServer {
    private readonly server;
    private readonly index;
    private readonly fileWatcher;
    private readonly config;
    constructor(config: ServerConfig);
    /**
     * Setup MCP tools
     */
    private setupTools;
    /**
     * Setup event handlers for index and file watcher
     */
    private setupEventHandlers;
    /**
     * Start the MCP server with STDIO transport
     */
    start(): Promise<void>;
    /**
     * Stop the MCP server
     */
    stop(): Promise<void>;
    /**
     * Get the underlying index for advanced operations
     */
    getIndex(): TranslationIndex;
    /**
     * Get the file watcher for advanced operations
     */
    getFileWatcher(): TranslationFileWatcher;
}
//# sourceMappingURL=mcp-server.d.ts.map