/**
 * Main MCP server implementation for i18n translation management
 */
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { z } from 'zod';
import { TranslationIndex } from '../core/translation-index.js';
import { TranslationFileWatcher } from '../core/file-watcher.js';
/**
 * High-level MCP server for translation management
 */
export class TranslationMCPServer {
    server;
    index;
    fileWatcher;
    config;
    constructor(config) {
        this.config = {
            baseLanguage: 'en',
            debug: false,
            watchOptions: {
                debounceMs: 100,
                ignored: ['**/node_modules/**', '**/.git/**']
            },
            ...config
        };
        // Initialize core components
        this.index = new TranslationIndex({
            baseLanguage: this.config.baseLanguage,
            debug: this.config.debug
        });
        this.fileWatcher = new TranslationFileWatcher({
            translationDir: this.config.translationDir,
            debounceMs: this.config.watchOptions.debounceMs,
            ignored: this.config.watchOptions.ignored,
            debug: this.config.debug
        }, this.index);
        // Initialize MCP server
        this.server = new McpServer({
            name: this.config.name,
            version: this.config.version,
        }, {
            capabilities: {
                tools: {},
                resources: {},
                prompts: {}
            }
        });
        this.setupTools();
        this.setupEventHandlers();
        if (this.config.debug) {
            console.log(`🚀 TranslationMCPServer initialized: ${this.config.name} v${this.config.version}`);
        }
    }
    /**
     * Setup MCP tools
     */
    setupTools() {
        // Search translation tool
        this.server.tool('search_translation', {
            query: z.string().describe('Search query for keys or values'),
            scope: z.enum(['keys', 'values', 'both']).default('both').describe('Search scope'),
            languages: z.array(z.string()).optional().describe('Specific languages to search in'),
            maxResults: z.number().min(1).max(100).default(20).describe('Maximum number of results'),
            caseSensitive: z.boolean().default(false).describe('Case sensitive search')
        }, async ({ query, scope, languages, maxResults, caseSensitive }) => {
            try {
                const results = await this.index.search(query, {
                    scope,
                    languages,
                    maxResults,
                    caseSensitive
                });
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({
                                query,
                                scope,
                                resultsCount: results.length,
                                results: results.map(result => ({
                                    keyPath: result.keyPath,
                                    matchType: result.matchType,
                                    score: result.score,
                                    translations: result.translations
                                }))
                            }, null, 2)
                        }]
                };
            }
            catch (error) {
                return {
                    content: [{
                            type: 'text',
                            text: `Error searching translations: ${error instanceof Error ? error.message : 'Unknown error'}`
                        }]
                };
            }
        });
        // Get translation context tool
        this.server.tool('get_translation_context', {
            keyPath: z.string().describe('Translation key path (e.g., "common.buttons.submit")'),
            contextDepth: z.number().min(0).max(5).default(1).describe('Depth of context to retrieve'),
            languages: z.union([z.array(z.string()), z.literal('all')]).default('all').describe('Languages to include')
        }, async ({ keyPath, contextDepth, languages }) => {
            try {
                const context = await this.index.getContext(keyPath, {
                    depth: contextDepth,
                    languages: languages === 'all' ? undefined : languages
                });
                if (!context) {
                    return {
                        content: [{
                                type: 'text',
                                text: `Translation key not found: ${keyPath}`
                            }]
                    };
                }
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({
                                keyPath: context.keyPath,
                                translations: context.translations,
                                parent: context.parent,
                                children: context.children.slice(0, 10), // Limit children for readability
                                siblings: context.siblings.slice(0, 10) // Limit siblings for readability
                            }, null, 2)
                        }]
                };
            }
            catch (error) {
                return {
                    content: [{
                            type: 'text',
                            text: `Error getting translation context: ${error instanceof Error ? error.message : 'Unknown error'}`
                        }]
                };
            }
        });
        // Update translation tool
        this.server.tool('update_translation', {
            keyPath: z.string().describe('Translation key path to update'),
            updates: z.record(z.string(), z.any()).describe('Language-value pairs to update'),
            validateStructure: z.boolean().default(true).describe('Validate structure consistency')
        }, async ({ keyPath, updates, validateStructure }) => {
            try {
                // Validate structure if requested
                if (validateStructure) {
                    const validation = await this.index.validateStructure();
                    if (!validation.valid) {
                        return {
                            content: [{
                                    type: 'text',
                                    text: `Structure validation failed before update: ${JSON.stringify(validation.structuralIssues, null, 2)}`
                                }]
                        };
                    }
                }
                // Perform updates
                const operations = Object.entries(updates).map(([language, value]) => ({
                    type: 'set',
                    keyPath,
                    language,
                    value
                }));
                const result = await this.index.batchUpdate(operations);
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({
                                success: result.success,
                                keyPath,
                                updatedLanguages: Object.keys(updates),
                                errors: result.errors
                            }, null, 2)
                        }]
                };
            }
            catch (error) {
                return {
                    content: [{
                            type: 'text',
                            text: `Error updating translation: ${error instanceof Error ? error.message : 'Unknown error'}`
                        }]
                };
            }
        });
        // Validate structure tool
        this.server.tool('validate_structure', {
            baseLanguage: z.string().optional().describe('Base language for validation'),
            fix: z.boolean().default(false).describe('Auto-fix missing translations')
        }, async ({ baseLanguage, fix }) => {
            try {
                const validation = await this.index.validateStructure({
                    baseLanguage: baseLanguage || this.config.baseLanguage,
                    autoFix: fix
                });
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({
                                valid: validation.valid,
                                summary: {
                                    missingKeysCount: Object.values(validation.missingKeys).reduce((sum, keys) => sum + keys.length, 0),
                                    extraKeysCount: Object.values(validation.extraKeys).reduce((sum, keys) => sum + keys.length, 0),
                                    typeMismatchesCount: validation.typeMismatches.length
                                },
                                details: {
                                    missingKeys: validation.missingKeys,
                                    extraKeys: validation.extraKeys,
                                    typeMismatches: validation.typeMismatches,
                                    structuralIssues: validation.structuralIssues
                                }
                            }, null, 2)
                        }]
                };
            }
            catch (error) {
                return {
                    content: [{
                            type: 'text',
                            text: `Error validating structure: ${error instanceof Error ? error.message : 'Unknown error'}`
                        }]
                };
            }
        });
        // Get server statistics tool
        this.server.tool('get_stats', {
            includeDetails: z.boolean().default(false).describe('Include detailed statistics')
        }, async ({ includeDetails }) => {
            try {
                const indexStats = this.index.getStats();
                const watcherStats = this.fileWatcher.getStats();
                const stats = {
                    server: {
                        name: this.config.name,
                        version: this.config.version,
                        baseLanguage: this.config.baseLanguage
                    },
                    index: indexStats,
                    watcher: watcherStats
                };
                if (includeDetails) {
                    return {
                        content: [{
                                type: 'text',
                                text: JSON.stringify({
                                    ...stats,
                                    details: {
                                        allLanguages: this.index.getLanguages(),
                                        sampleKeys: this.index.getKeys().slice(0, 10),
                                        watchedFiles: this.fileWatcher.getWatchedFiles().slice(0, 10)
                                    }
                                }, null, 2)
                            }]
                    };
                }
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify(stats, null, 2)
                        }]
                };
            }
            catch (error) {
                return {
                    content: [{
                            type: 'text',
                            text: `Error getting statistics: ${error instanceof Error ? error.message : 'Unknown error'}`
                        }]
                };
            }
        });
        if (this.config.debug) {
            console.log('🔧 MCP tools registered successfully');
        }
    }
    /**
     * Setup event handlers for index and file watcher
     */
    setupEventHandlers() {
        // Index events
        this.index.on('set', (event) => {
            if (this.config.debug) {
                console.log(`📝 Translation set: ${event.keyPath} [${event.language}]`);
            }
        });
        this.index.on('delete', (event) => {
            if (this.config.debug) {
                console.log(`🗑️ Translation deleted: ${event.keyPath} [${event.language || 'all'}]`);
            }
        });
        // File watcher events
        this.fileWatcher.on('fileProcessed', (event) => {
            if (this.config.debug) {
                console.log(`📄 File processed: ${event.type} ${event.path} [${event.language}]`);
            }
        });
        this.fileWatcher.on('error', (error) => {
            console.error('📁 File watcher error:', error);
        });
        this.fileWatcher.on('ready', () => {
            if (this.config.debug) {
                console.log('📁 File watcher ready');
            }
        });
    }
    /**
     * Start the MCP server with STDIO transport
     */
    async start() {
        try {
            // Initialize file watcher first
            await this.fileWatcher.start();
            // Connect to STDIO transport
            const stdioTransport = new StdioServerTransport();
            await this.server.connect(stdioTransport);
            console.log(`🚀 Translation MCP Server started: ${this.config.name} v${this.config.version}`);
            console.log(`📁 Watching translations in: ${this.config.translationDir}`);
            console.log(`🌐 Base language: ${this.config.baseLanguage}`);
        }
        catch (error) {
            console.error('❌ Failed to start MCP server:', error);
            throw error;
        }
    }
    /**
     * Stop the MCP server
     */
    async stop() {
        try {
            await this.fileWatcher.stop();
            // Note: MCP SDK doesn't provide explicit server.stop() method
            console.log('🛑 Translation MCP Server stopped');
        }
        catch (error) {
            console.error('❌ Error stopping MCP server:', error);
            throw error;
        }
    }
    /**
     * Get the underlying index for advanced operations
     */
    getIndex() {
        return this.index;
    }
    /**
     * Get the file watcher for advanced operations
     */
    getFileWatcher() {
        return this.fileWatcher;
    }
}
//# sourceMappingURL=mcp-server.js.map