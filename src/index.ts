#!/usr/bin/env node

/**
 * Main entry point for the i18n MCP server
 */

import { TranslationMCPServer } from './server/mcp-server.js';
import { ServerConfig } from './types/translation.js';
import { promises as fs } from 'fs';
import { resolve } from 'path';

/**
 * Default configuration
 */
const DEFAULT_CONFIG: Partial<ServerConfig> = {
  name: 'i18n-mcp',
  version: '1.0.0',
  baseLanguage: 'en',
  debug: false,
  watchOptions: {
    debounceMs: 100,
    ignored: ['**/node_modules/**', '**/.git/**', '**/dist/**', '**/build/**']
  }
};

/**
 * Parse command line arguments
 */
function parseArgs(): Partial<ServerConfig> {
  const args = process.argv.slice(2);
  const config: Partial<ServerConfig> = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    const nextArg = args[i + 1];

    switch (arg) {
      case '--dir':
      case '-d':
        if (nextArg && !nextArg.startsWith('-')) {
          config.translationDir = resolve(nextArg);
          i++;
        }
        break;
      
      case '--base-language':
      case '-b':
        if (nextArg && !nextArg.startsWith('-')) {
          config.baseLanguage = nextArg;
          i++;
        }
        break;
      
      case '--debug':
        config.debug = true;
        break;
      
      case '--name':
      case '-n':
        if (nextArg && !nextArg.startsWith('-')) {
          config.name = nextArg;
          i++;
        }
        break;
      
      case '--version':
      case '-v':
        if (nextArg && !nextArg.startsWith('-')) {
          config.version = nextArg;
          i++;
        }
        break;
      
      case '--help':
      case '-h':
        printHelp();
        process.exit(0);
        break;
      
      default:
        if (arg && arg.startsWith('-')) {
          console.error(`Unknown option: ${arg}`);
          printHelp();
          process.exit(1);
        }
        // Assume it's a directory path if no flag is provided
        if (arg && !config.translationDir) {
          config.translationDir = resolve(arg);
        }
        break;
    }
  }

  return config;
}

/**
 * Print help information
 */
function printHelp(): void {
  console.log(`
i18n MCP Server - High-performance translation file management

Usage: i18n-mcp [options] [translation-directory]

Options:
  -d, --dir <path>           Translation files directory (default: ./locales)
  -b, --base-language <lang> Base language for structure template (default: en)
  -n, --name <name>          Server name (default: i18n-mcp)
  -v, --version <version>    Server version (default: 1.0.0)
  --debug                    Enable debug logging
  -h, --help                 Show this help message

Examples:
  i18n-mcp ./locales                    # Watch ./locales directory
  i18n-mcp --dir ./i18n --debug        # Watch ./i18n with debug logging
  i18n-mcp --base-language fr ./locales # Use French as base language

Environment Variables:
  I18N_MCP_DIR              Translation directory
  I18N_MCP_BASE_LANGUAGE    Base language
  I18N_MCP_DEBUG            Enable debug mode (true/false)
`);
}

/**
 * Load configuration from environment variables
 */
function loadEnvConfig(): Partial<ServerConfig> {
  const config: Partial<ServerConfig> = {};

  if (process.env.I18N_MCP_DIR) {
    config.translationDir = resolve(process.env.I18N_MCP_DIR);
  }

  if (process.env.I18N_MCP_BASE_LANGUAGE) {
    config.baseLanguage = process.env.I18N_MCP_BASE_LANGUAGE;
  }

  if (process.env.I18N_MCP_DEBUG) {
    config.debug = process.env.I18N_MCP_DEBUG.toLowerCase() === 'true';
  }

  return config;
}

/**
 * Validate and resolve configuration
 */
async function resolveConfig(): Promise<ServerConfig> {
  const envConfig = loadEnvConfig();
  const argsConfig = parseArgs();
  
  const config: ServerConfig = {
    ...DEFAULT_CONFIG,
    ...envConfig,
    ...argsConfig
  } as ServerConfig;

  // Determine translation directory
  if (!config.translationDir) {
    // Try common default locations
    const defaultPaths = ['./locales', './i18n', './translations', './lang'];
    
    for (const defaultPath of defaultPaths) {
      try {
        const resolvedPath = resolve(defaultPath);
        await fs.access(resolvedPath);
        config.translationDir = resolvedPath;
        break;
      } catch {
        // Continue to next path
      }
    }
    
    if (!config.translationDir) {
      console.error('❌ No translation directory found. Please specify with --dir or create ./locales directory.');
      console.error('   Use --help for more information.');
      process.exit(1);
    }
  }

  // Validate translation directory exists
  try {
    const stats = await fs.stat(config.translationDir);
    if (!stats.isDirectory()) {
      console.error(`❌ Translation path is not a directory: ${config.translationDir}`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Translation directory does not exist: ${config.translationDir}`);
    console.error('   Create the directory or specify a different path with --dir');
    process.exit(1);
  }

  return config;
}

/**
 * Setup graceful shutdown
 */
function setupGracefulShutdown(server: TranslationMCPServer): void {
  const shutdown = async (signal: string) => {
    console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);
    
    try {
      await server.stop();
      console.log('✅ Server stopped successfully');
      process.exit(0);
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  };

  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  
  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught exception:', error);
    shutdown('uncaughtException');
  });
  
  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled rejection at:', promise, 'reason:', reason);
    shutdown('unhandledRejection');
  });
}

/**
 * Main function
 */
async function main(): Promise<void> {
  try {
    console.log('🚀 Starting i18n MCP Server...\n');
    
    // Resolve configuration
    const config = await resolveConfig();
    
    if (config.debug) {
      console.log('🔧 Configuration:', JSON.stringify(config, null, 2));
    }
    
    // Create and start server
    const server = new TranslationMCPServer(config);
    
    // Setup graceful shutdown
    setupGracefulShutdown(server);
    
    // Start the server
    await server.start();
    
    // Server is now running and will handle MCP requests via STDIO
    
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Run the main function
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
}

export { TranslationMCPServer };
export * from './types/translation.js';
export * from './core/translation-index.js';
export * from './core/file-watcher.js';
