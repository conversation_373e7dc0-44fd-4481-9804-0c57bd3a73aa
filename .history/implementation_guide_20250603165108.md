# Translation MCP Server Implementation Guide - TypeScript 2025

## Technology Stack & Dependencies

### Core MCP Framework
```bash
# Official MCP TypeScript SDK (latest stable)
npm install @modelcontextprotocol/sdk@latest zod

# Development dependencies
npm install -D typescript@^5.0 @types/node tsx nodemon

# File system monitoring
npm install chokidar@^4.0  # Rewritten in TS, optimized performance

# Performance & Utilities
npm install lru-cache@^10.0 fast-json-patch@^3.1
```

### Advanced Dependencies for Performance
```bash
# For high-performance indexing (optional)
npm install trie-prefix-tree@^1.5  # Or implement custom ART

# For atomic operations and conflict resolution
npm install proper-lockfile@^4.1

# For graceful shutdown and error recovery
npm install graceful-fs@^4.2
```

## Project Structure

```
src/
├── server/
│   ├── mcp-server.ts          # Main MCP server implementation
│   ├── transports/
│   │   ├── stdio.ts           # STDIO transport for local usage
│   │   └── streamable-http.ts # HTTP transport for remote usage
├── core/
│   ├── translation-index.ts   # In-memory indexed database
│   ├── structure-template.ts  # Structure template system
│   ├── file-watcher.ts        # Chokidar-based file monitoring
│   ├── cache-manager.ts       # LRU cache with invalidation
│   └── conflict-resolver.ts   # Atomic operations & conflict handling
├── tools/
│   ├── search-translation.ts
│   ├── get-translation-context.ts
│   ├── update-translation.ts
│   ├── add-translation-key.ts
│   ├── validate-structure.ts
│   ├── batch-update.ts
│   ├── get-structure-tree.ts
│   └── analyze-usage.ts
├── types/
│   ├── translation.ts         # Core type definitions
│   └── mcp-tools.ts          # Tool schema definitions
└── utils/
    ├── path-parser.ts         # Dot notation path utilities
    ├── json-operations.ts     # Safe JSON parsing/writing
    └── validation.ts          # Zod schemas
```

## Core Implementation Architecture

### 1. High-Performance Translation Index

```typescript
// src/core/translation-index.ts
import { LRUCache } from 'lru-cache';
import { EventEmitter } from 'events';

interface TranslationEntry {
  value: any;
  file: string;
  line: number;
  column: number;
  lastModified: number;
}

interface IndexedTranslation {
  [language: string]: TranslationEntry;
}

export class TranslationIndex extends EventEmitter {
  private readonly flatIndex = new Map<string, IndexedTranslation>();
  private readonly reverseIndex = new Map<string, Set<string>>(); // value -> paths
  private readonly structureTemplate = new Map<string, any>();
  private readonly cache = new LRUCache<string, any>({ max: 10000 });
  
  // Binary search optimized sorted keys for prefix operations
  private sortedKeys: string[] = [];
  private keysDirty = false;

  constructor(private readonly baseLanguage: string = 'en') {
    super();
  }

  // O(1) lookup with caching
  get(keyPath: string, language?: string): TranslationEntry | undefined {
    const cacheKey = `${keyPath}:${language || 'all'}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const entry = this.flatIndex.get(keyPath);
    if (!entry) return undefined;

    const result = language ? entry[language] : entry;
    this.cache.set(cacheKey, result);
    return result;
  }

  // Optimized prefix search using binary search
  searchByPrefix(prefix: string): string[] {
    this.ensureSortedKeys();
    
    const startIdx = this.binarySearchStart(prefix);
    const endIdx = this.binarySearchEnd(prefix);
    
    return this.sortedKeys.slice(startIdx, endIdx + 1);
  }

  private binarySearchStart(prefix: string): number {
    let left = 0, right = this.sortedKeys.length;
    
    while (left < right) {
      const mid = Math.floor((left + right) / 2);
      if (this.sortedKeys[mid] < prefix) left = mid + 1;
      else right = mid;
    }
    
    return left;
  }

  private ensureSortedKeys(): void {
    if (this.keysDirty) {
      this.sortedKeys = Array.from(this.flatIndex.keys()).sort();
      this.keysDirty = false;
    }
  }

  // Batch operations for performance
  batchUpdate(operations: Array<{
    type: 'set' | 'delete';
    keyPath: string;
    language?: string;
    value?: any;
  }>): void {
    // Transaction-like batch processing
    const backup = new Map(this.flatIndex);
    
    try {
      for (const op of operations) {
        if (op.type === 'set') {
          this.setInternal(op.keyPath, op.language!, op.value);
        } else {
          this.deleteInternal(op.keyPath, op.language);
        }
      }
      
      this.keysDirty = true;
      this.cache.clear();
      this.emit('batchUpdate', operations);
      
    } catch (error) {
      // Rollback on error
      this.flatIndex.clear();
      backup.forEach((value, key) => this.flatIndex.set(key, value));
      throw error;
    }
  }

  private setInternal(keyPath: string, language: string, value: any): void {
    let entry = this.flatIndex.get(keyPath);
    if (!entry) {
      entry = {} as IndexedTranslation;
      this.flatIndex.set(keyPath, entry);
    }
    
    entry[language] = {
      value,
      file: '', // Will be set by file watcher
      line: 0,
      column: 0,
      lastModified: Date.now()
    };
  }
}
```

### 2. Smart File Watcher with Chokidar v4

```typescript
// src/core/file-watcher.ts
import chokidar from 'chokidar';
import { promises as fs } from 'fs';
import { TranslationIndex } from './translation-index.js';
import { debounce } from './utils.js';

export class TranslationFileWatcher {
  private watcher?: chokidar.FSWatcher;
  private readonly debouncedProcessChange: (path: string) => void;

  constructor(
    private readonly translationDir: string,
    private readonly index: TranslationIndex
  ) {
    // Debounce file changes to handle rapid successive writes
    this.debouncedProcessChange = debounce(this.processFileChange.bind(this), 100);
  }

  async start(): Promise<void> {
    this.watcher = chokidar.watch(this.translationDir, {
      // Optimized settings for performance
      persistent: true,
      ignoreInitial: false,
      followSymlinks: false,
      
      // Use native fsevents on macOS, fs.watch elsewhere
      usePolling: false,
      
      // Only watch JSON files
      ignored: (path: string) => !path.endsWith('.json'),
      
      // Performance optimizations
      alwaysStat: false,
      depth: 2, // Limit recursion depth
      
      // Reduce OS resource usage
      awaitWriteFinish: {
        stabilityThreshold: 100,
        pollInterval: 50
      }
    });

    this.watcher
      .on('add', (path) => this.debouncedProcessChange(path))
      .on('change', (path) => this.debouncedProcessChange(path))
      .on('unlink', (path) => this.handleFileDelete(path))
      .on('error', (error) => console.error('Watcher error:', error));

    console.log(`📁 Watching translation files in: ${this.translationDir}`);
  }

  async stop(): Promise<void> {
    if (this.watcher) {
      await this.watcher.close();
      this.watcher = undefined;
    }
  }

  private async processFileChange(filePath: string): Promise<void> {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const translations = JSON.parse(content);
      
      // Extract language from filename (e.g., en.json -> en)
      const language = this.extractLanguageFromPath(filePath);
      
      // Update index with parsed translations
      this.updateIndexFromTranslations(translations, language, filePath);
      
    } catch (error) {
      console.error(`Failed to process file ${filePath}:`, error);
    }
  }

  private extractLanguageFromPath(filePath: string): string {
    const basename = filePath.split('/').pop() || '';
    return basename.replace('.json', '');
  }

  private updateIndexFromTranslations(
    translations: any,
    language: string,
    filePath: string,
    prefix = ''
  ): void {
    for (const [key, value] of Object.entries(translations)) {
      const fullPath = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        // Recursively process nested objects
        this.updateIndexFromTranslations(value, language, filePath, fullPath);
      } else {
        // Leaf node - add to index
        this.index.set(fullPath, language, value, { filePath });
      }
    }
  }
}
```

### 3. Modern MCP Server with Streamable HTTP Support

```typescript
// src/server/mcp-server.ts
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { z } from 'zod';
import { TranslationIndex } from '../core/translation-index.js';
import { TranslationFileWatcher } from '../core/file-watcher.js';
import { createSearchTranslationTool } from '../tools/search-translation.js';

export class TranslationMCPServer {
  private readonly server: McpServer;
  private readonly index: TranslationIndex;
  private readonly fileWatcher: TranslationFileWatcher;

  constructor(private readonly config: {
    name: string;
    version: string;
    translationDir: string;
    baseLanguage?: string;
  }) {
    this.index = new TranslationIndex(config.baseLanguage);
    this.fileWatcher = new TranslationFileWatcher(config.translationDir, this.index);
    
    this.server = new McpServer({
      name: config.name,
      version: config.version,
    }, {
      capabilities: {
        tools: {},
        resources: {},
        prompts: {}
      }
    });

    this.setupTools();
    this.setupResources();
    this.setupPrompts();
  }

  private setupTools(): void {
    // Search translation tool with semantic search
    this.server.tool(
      'search_translation',
      {
        query: z.string().describe('Search query for keys or values'),
        scope: z.enum(['keys', 'values', 'both']).default('both'),
        languages: z.array(z.string()).optional(),
        maxResults: z.number().min(1).max(100).default(20)
      },
      async ({ query, scope, languages, maxResults }) => {
        const results = await this.index.search(query, {
          scope,
          languages,
          maxResults
        });

        return {
          content: [{
            type: 'text',
            text: JSON.stringify(results, null, 2)
          }]
        };
      }
    );

    // Get translation context tool
    this.server.tool(
      'get_translation_context',
      {
        keyPath: z.string().describe('Translation key path (e.g., "common.buttons.submit")'),
        contextDepth: z.number().min(0).max(5).default(1),
        languages: z.union([z.array(z.string()), z.literal('all')]).default('all')
      },
      async ({ keyPath, contextDepth, languages }) => {
        const context = await this.index.getContext(keyPath, {
          depth: contextDepth,
          languages: languages === 'all' ? undefined : languages
        });

        return {
          content: [{
            type: 'text',
            text: JSON.stringify(context, null, 2)
          }]
        };
      }
    );

    // Add more tools following the same pattern...
    this.setupUpdateTools();
    this.setupAnalysisTools();
  }

  private setupUpdateTools(): void {
    // Update translation tool with validation
    this.server.tool(
      'update_translation',
      {
        keyPath: z.string(),
        updates: z.record(z.string(), z.any()),
        validateStructure: z.boolean().default(true)
      },
      async ({ keyPath, updates, validateStructure }) => {
        if (validateStructure) {
          await this.index.validateUpdate(keyPath, updates);
        }

        const result = await this.index.batchUpdate([{
          type: 'set',
          keyPath,
          updates
        }]);

        return {
          content: [{
            type: 'text',
            text: JSON.stringify(result, null, 2)
          }]
        };
      }
    );

    // Batch update tool for atomic operations
    this.server.tool(
      'batch_update',
      {
        operations: z.array(z.object({
          action: z.enum(['add', 'update', 'delete']),
          keyPath: z.string(),
          values: z.record(z.string(), z.any()).optional()
        })),
        validateAll: z.boolean().default(true)
      },
      async ({ operations, validateAll }) => {
        const result = await this.index.atomicBatchUpdate(operations, {
          validate: validateAll
        });

        return {
          content: [{
            type: 'text',
            text: JSON.stringify(result, null, 2)
          }]
        };
      }
    );
  }

  private setupAnalysisTools(): void {
    // Structure validation tool
    this.server.tool(
      'validate_structure',
      {
        baseLanguage: z.string().optional(),
        fix: z.boolean().default(false)
      },
      async ({ baseLanguage, fix }) => {
        const validation = await this.index.validateStructure({
          baseLanguage: baseLanguage || this.config.baseLanguage,
          autoFix: fix
        });

        return {
          content: [{
            type: 'text',
            text: JSON.stringify(validation, null, 2)
          }]
        };
      }
    );

    // Usage analysis tool
    this.server.tool(
      'analyze_usage',
      {
        codebasePath: z.string().optional(),
        checkDuplicateValues: z.boolean().default(true)
      },
      async ({ codebasePath, checkDuplicateValues }) => {
        const analysis = await this.index.analyzeUsage({
          codebasePath,
          checkDuplicates: checkDuplicateValues
        });

        return {
          content: [{
            type: 'text',
            text: JSON.stringify(analysis, null, 2)
          }]
        };
      }
    );
  }

  async start(transport: 'stdio' | 'http' = 'stdio'): Promise<void> {
    // Initialize file watcher first
    await this.fileWatcher.start();
    
    if (transport === 'stdio') {
      const stdioTransport = new StdioServerTransport();
      await this.server.connect(stdioTransport);
      console.log('🚀 Translation MCP Server started with STDIO transport');
    } else {
      // For HTTP transport, you'd implement StreamableHttpTransport
      // following the 2025-03-26 spec
      throw new Error('HTTP transport not implemented yet');
    }
  }

  async stop(): Promise<void> {
    await this.fileWatcher.stop();
    // MCP server cleanup would go here
    console.log('🛑 Translation MCP Server stopped');
  }
}
```

### 4. Advanced Performance Optimizations

```typescript
// src/utils/performance.ts

// Debounce utility for file watching
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Binary search utilities for sorted operations
export class SortedArray<T> {
  private items: T[] = [];
  
  constructor(private compareFunc: (a: T, b: T) => number) {}
  
  insert(item: T): void {
    const index = this.binarySearch(item);
    this.items.splice(index, 0, item);
  }
  
  private binarySearch(target: T): number {
    let left = 0, right = this.items.length;
    
    while (left < right) {
      const mid = Math.floor((left + right) / 2);
      if (this.compareFunc(this.items[mid], target) < 0) {
        left = mid + 1;
      } else {
        right = mid;
      }
    }
    
    return left;
  }
}

// Memory-efficient path parsing
export class PathParser {
  private static readonly CACHE = new Map<string, string[]>();
  
  static parse(path: string): string[] {
    if (this.CACHE.has(path)) {
      return this.CACHE.get(path)!;
    }
    
    const segments = path.split('.');
    this.CACHE.set(path, segments);
    
    // Prevent memory leaks
    if (this.CACHE.size > 10000) {
      const firstKey = this.CACHE.keys().next().value;
      this.CACHE.delete(firstKey);
    }
    
    return segments;
  }
}
```

## Key Implementation Insights

### 1. **Use Official MCP TypeScript SDK**
- Latest stable version with Streamable HTTP support (2025-03-26 spec)
- Proper type safety with Zod schema validation
- Follows MCP best practices for tool definitions

### 2. **Chokidar v4 for File Watching**
- Rewritten in TypeScript with minimal dependencies
- Uses native fsevents on macOS for optimal performance
- Configurable polling vs. event-based watching

### 3. **High-Performance Indexing**
- Use JavaScript Maps for O(1) lookups
- Implement binary search for prefix operations
- LRU cache for frequently accessed translations
- Consider Adaptive Radix Tree for complex hierarchical lookups

### 4. **Memory Management**
- Debounced file operations to prevent thrashing
- Cache invalidation strategies
- Batch operations for atomic updates
- Graceful error recovery with transaction rollback

### 5. **Transport Flexibility**
- STDIO for local development and CLI integration
- Streamable HTTP for remote server deployment
- Session management for stateful operations

This implementation leverages 2025's state-of-the-art technologies while maintaining the architectural principles you outlined. The result is a highly performant, scalable translation MCP server that can handle large translation files efficiently.