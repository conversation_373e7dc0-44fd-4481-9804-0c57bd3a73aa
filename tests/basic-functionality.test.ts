#!/usr/bin/env tsx

/**
 * Basic functionality test for the i18n MCP server
 */

import { TranslationIndex } from '../src/core/translation-index.js';
import { JsonOperations } from '../src/utils/json-operations.js';
import { resolve } from 'path';

async function runBasicTests() {
  console.log('🧪 Running Basic Functionality Tests...\n');

  // Create index
  const index = new TranslationIndex({
    baseLanguage: 'en',
    debug: false
  });

  try {
    // Manually load translation files
    const files = [
      { path: resolve('./locales/en.json'), language: 'en' },
      { path: resolve('./locales/es.json'), language: 'es' },
      { path: resolve('./locales/fr.json'), language: 'fr' }
    ];

    console.log('📄 Loading translation files...');
    for (const { path, language } of files) {
      const parseResult = await JsonOperations.parseFile(path);
      await addTranslationsToIndex(index, parseResult.data, language, path);
    }

    // Test 1: Index Statistics
    console.log('\n✅ Test 1: Index Statistics');
    const stats = index.getStats();
    console.log(`   Total Keys: ${stats.totalKeys}`);
    console.log(`   Total Translations: ${stats.totalTranslations}`);
    console.log(`   Languages: ${stats.languages.join(', ')}`);
    
    if (stats.totalKeys === 0) {
      throw new Error('No translations loaded');
    }

    // Test 2: Search Functionality
    console.log('\n✅ Test 2: Search Functionality');
    const searchResults = await index.search('submit', { scope: 'both', maxResults: 5 });
    console.log(`   Found ${searchResults.length} results for "submit"`);
    
    if (searchResults.length === 0) {
      throw new Error('Search should find "submit" translation');
    }

    // Test 3: Context Retrieval
    console.log('\n✅ Test 3: Context Retrieval');
    const context = await index.getContext('common.buttons.submit', { depth: 1 });
    
    if (!context) {
      throw new Error('Context should be found for "common.buttons.submit"');
    }
    
    console.log(`   Found context with ${context.siblings.length} siblings`);

    // Test 4: Structure Validation
    console.log('\n✅ Test 4: Structure Validation');
    const validation = await index.validateStructure();
    const missingCount = Object.values(validation.missingKeys).reduce((sum, keys) => sum + keys.length, 0);
    console.log(`   Structure valid: ${validation.valid}`);
    console.log(`   Missing keys: ${missingCount}`);

    // Test 5: Prefix Search
    console.log('\n✅ Test 5: Prefix Search');
    const prefixResults = index.searchByPrefix('common.buttons');
    console.log(`   Found ${prefixResults.length} keys with prefix "common.buttons"`);
    
    if (prefixResults.length === 0) {
      throw new Error('Prefix search should find common.buttons keys');
    }

    // Test 6: Language Coverage
    console.log('\n✅ Test 6: Language Coverage');
    const languages = index.getLanguages();
    console.log(`   Detected languages: ${languages.join(', ')}`);
    
    if (!languages.includes('en') || !languages.includes('es') || !languages.includes('fr')) {
      throw new Error('Should detect en, es, and fr languages');
    }

    console.log('\n🎉 All basic functionality tests passed!');
    return true;

  } catch (error) {
    console.error('\n❌ Test failed:', error);
    return false;
  }
}

async function addTranslationsToIndex(
  index: TranslationIndex,
  translations: any,
  language: string,
  filePath: string,
  prefix = ''
): Promise<void> {
  if (!translations || typeof translations !== 'object') {
    return;
  }

  for (const [key, value] of Object.entries(translations)) {
    const fullPath = prefix ? `${prefix}.${key}` : key;
    
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      // Recursively process nested objects
      await addTranslationsToIndex(index, value, language, filePath, fullPath);
    } else {
      // Leaf node - add to index
      index.set(fullPath, language, value, { 
        file: filePath,
        line: 0,
        column: 0
      });
    }
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runBasicTests()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error('Fatal error:', error);
      process.exit(1);
    });
}

export { runBasicTests };
